import gc
import weakref
import tracemalloc
import sys

class DataHandler:
    def __init__(self):
        self.data = []
        self._max_data_size = 100  # 限制存储的数据量
        tracemalloc.start()  # 启动内存跟踪
    
    def __del__(self):
        tracemalloc.stop()  # 停止内存跟踪
    
    def process_large_data(self):
        """处理大数据，确保及时释放内存"""
        def data_generator():
            for _ in range(100):
                data = bytearray(1024*1024)  # 1MB数据
                yield data
                del data  # 显式释放内存
        
        # 清空旧数据，防止内存累积
        if len(self.data) >= self._max_data_size:
            self.data.clear()
        
        # 使用生成器表达式处理数据
        processed = (len(data) for data in data_generator())
        self.data.append(list(processed))  # 仅存储处理结果
    
    def memory_usage(self):
        """返回当前内存使用情况"""
        current, peak = tracemalloc.get_traced_memory()
        return {
            'current': f"{current / 10**6:.2f} MB",
            'peak': f"{peak / 10**6:.2f} MB"
        }

def test_memory_management():
    """测试内存管理功能"""
    print("Testing memory management...")
    handler = DataHandler()
    
    try:
        # 测试大数据处理
        for i in range(3):  # 减少测试数据量
            handler.process_large_data()
            print(f"Iteration {i+1}: {handler.memory_usage()}")
        
        # 强制垃圾回收
        print("Running garbage collection...")
        gc.collect()
        print("After GC:", handler.memory_usage())
        print("Memory management test completed successfully")
    except Exception as e:
        print(f"Error during memory test: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(test_memory_management())