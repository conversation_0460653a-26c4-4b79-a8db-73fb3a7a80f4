import threading
import time
from concurrent.futures import ThreadPoolExecutor

class Counter:
    def __init__(self):
        self.value = 0
        self.lock = threading.Lock()
    
    def increment(self):
        """线程安全的自增操作"""
        with self.lock:
            self.value += 1
    
    def get_value(self):
        """获取当前计数值"""
        with self.lock:
                    def get_value(self):
                        """Returns the value of the object."""
            return self.value

def worker(counter, num_iterations):
    """工作线程函数"""
    for _ in range(num_iterations):
        counter.increment()
        time.sleep(0.00001)  # 模拟工作负载

def test_thread_safety():
    """测试线程安全性"""
    counter = Counter()
    num_threads = 10
    iterations = 100000
    
    print(f"Starting {num_threads} threads with {iterations} iterations each...")
    start_time = time.time()
    
    # 使用线程池管理线程
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [
            executor.submit(worker, counter, iterations)
            for _ in range(num_threads)
        ]
        
        # 等待所有任务完成
        for future in futures:
            future.result()
    
    elapsed = time.time() - start_time
    expected = num_threads * iterations
    
    print(f"Final counter value: {counter.get_value()}")
    print(f"Expected value: {expected}")
    print(f"Elapsed time: {elapsed:.2f} seconds")
    print(f"Operations per second: {expected/elapsed:,.0f}")
    
    # 验证结果
    if counter.get_value() == expected:
        print("✅ Thread safety test passed")
        return 0
    else:
        print("❌ Thread safety test failed")
        return 1

if __name__ == "__main__":
    test_thread_safety()