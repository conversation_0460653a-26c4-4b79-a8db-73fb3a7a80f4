class Node:
    def __init__(self, data):
        self.data = data
        self.next = None

class LinkedList:
    def __init__(self):
        self.head = None
        self.size = 0  # 添加size计数器
    
    def __len__(self):
        """返回链表长度"""
        return self.size
    
    def is_empty(self):
        """检查链表是否为空"""
        return self.size == 0
    
    def get_first(self):
        """获取第一个节点的数据"""
        if self.is_empty():
            raise ValueError("Linked list is empty")
        return self.head.data
    
    def append(self, data):
        """在链表末尾添加节点"""
        new_node = Node(data)
        if self.is_empty():
            self.head = new_node
        else:
            current = self.head
            while current.next is not None:
                current = current.next
            current.next = new_node
        self.size += 1
    
    def prepend(self, data):
        """在链表头部添加节点"""
        new_node = Node(data)
        new_node.next = self.head
        self.head = new_node
        self.size += 1
    
    def remove_first(self):
        """移除并返回第一个节点"""
        if self.is_empty():
            raise ValueError("Linked list is empty")
        removed = self.head
        self.head = self.head.next
        self.size -= 1
        return removed.data
    
    def remove_last(self):
        """移除并返回最后一个节点"""
        if self.is_empty():
            raise ValueError("Linked list is empty")
        if self.size == 1:
            return self.remove_first()
        
        current = self.head
        while current.next.next is not None:
            current = current.next
        removed = current.next
        current.next = None
        self.size -= 1
        return removed.data
    
    def print_list(self):
        """打印链表内容"""
        current = self.head
        elements = []
        while current is not None:
            elements.append(str(current.data))
            current = current.next
        print(" -> ".join(elements) + " -> None")
    
    def __str__(self):
        """字符串表示"""
        current = self.head
        elements = []
        while current is not None:
            elements.append(str(current.data))
            current = current.next
        return " -> ".join(elements) + " -> None"

import unittest

class TestLinkedList(unittest.TestCase):
    def setUp(self):
        self.ll = LinkedList()
    
    def test_append_and_length(self):
        self.assertEqual(len(self.ll), 0)
        self.ll.append(1)
        self.assertEqual(len(self.ll), 1)
        self.ll.append(2)
        self.assertEqual(len(self.ll), 2)
    
    def test_remove_first(self):
        self.ll.append(1)
        self.ll.append(2)
        self.assertEqual(self.ll.remove_first(), 1)
        self.assertEqual(len(self.ll), 1)
        self.assertEqual(self.ll.remove_first(), 2)
        self.assertEqual(len(self.ll), 0)
        with self.assertRaises(ValueError):
            self.ll.remove_first()
    
    def test_print_list(self):
        self.ll.append(1)
        self.ll.append(2)
        self.ll.append(3)
        self.assertEqual(str(self.ll), "1 -> 2 -> 3 -> None")

if __name__ == "__main__":
    unittest.main()