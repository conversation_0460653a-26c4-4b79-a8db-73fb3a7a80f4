def binary_search(arr, target):
    """优化后的二分查找实现"""
    if not arr:
        return -1
    
    left, right = 0, len(arr) - 1
    
    while left <= right:
        mid = left + (right - left) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    
    return -1  # 未找到目标值

    # 添加类型提示和文档示例
    """
    >>> binary_search([1, 2, 3, 4, 5], 3)
    2
    >>> binary_search([1, 2, 3, 4, 5], 6)
    -1
    """

def find_peak_element(nums):
    """
    优化后的峰值查找算法，时间复杂度O(log n)
    
    参数:
        nums: List[int] - 输入数组
        
    返回:
        int - 峰值元素的索引
        
    示例:
        >>> find_peak_element([1, 2, 1, 3, 5, 6, 4])
        5
        >>> find_peak_element([1, 2, 3, 1])
        2
    """
    if not nums:
        raise ValueError("Input array cannot be empty")
    
    n = len(nums)
    if n == 1:
        return 0
    
    # 使用二分查找优化
    left, right = 0, n - 1
    while left < right:
        mid = left + (right - left) // 2
        if nums[mid] < nums[mid + 1]:
            left = mid + 1
        else:
            right = mid
    
    return left

# 测试代码
if __name__ == "__main__":
    # 二分查找测试
    assert binary_search([], 1) == -1
    assert binary_search([1, 2, 3, 4, 5], 3) == 2
    assert binary_search([1, 2, 3, 4, 5], 6) == -1
    assert binary_search([1, 2, 3, 4, 5], 0) == -1
    
    # 峰值查找测试
    assert find_peak_element([1]) == 0
    assert find_peak_element([1, 2, 3, 4]) == 3
    assert find_peak_element([4, 3, 2, 1]) == 0
    assert find_peak_element([1, 3, 2]) == 1
    assert find_peak_element([1, 2, 1, 3, 5, 6, 4]) == 5
    assert find_peak_element([1, 2, 3, 1]) == 2
    
    print("所有测试通过！")