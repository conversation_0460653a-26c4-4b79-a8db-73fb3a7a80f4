import hashlib
import secrets
import string

class UserManager:
    def __init__(self):
        self.users = {}
        self.salt = secrets.token_hex(16)  # 添加全局salt
    
    def _hash_password(self, password):
        """使用PBKDF2和salt安全哈希密码"""
        return hashlib.pbkdf2_hmac(
            'sha256',
            password.encode(),
            self.salt.encode(),
            100000  # 迭代次数
        ).hex()
    
    def add_user(self, username, password):
        """添加用户，要求密码复杂度"""
        if not username or not password:
            raise ValueError("Username and password cannot be empty")
        if len(username) < 4:
            raise ValueError("Username must be at least 4 characters")
        if len(password) < 8:
            raise ValueError("Password must be at least 8 characters")
        if not any(c in string.punctuation for c in password):
            raise ValueError("Password must contain at least one special character")
        if username in self.users:
            raise ValueError("Username already exists")
        
        self.users[username] = {
            'password_hash': self._hash_password(password),
            'data': {}
        }
    
    def get_user(self, username):
        """获取用户信息，统一返回User对象或None"""
        if username not in self.users:
            return None
        return User(
            username=username,
            data=self.users[username]['data'].copy()
        )
    
    def update_user(self, username, new_username=None, new_password=None):
        """更新用户信息，包含完整的事务处理"""
        if username not in self.users:
            raise ValueError("User does not exist")
        
        # 验证新密码复杂度
        if new_password:
            if len(new_password) < 8:
                raise ValueError("Password must be at least 8 characters")
            if not any(c in string.punctuation for c in new_password):
                raise ValueError("Password must contain at least one special character")
        
        # 使用上下文管理器处理事务
        with self._transaction():
            if new_username:
                if new_username in self.users:
                    raise ValueError("New username already exists")
                if len(new_username) < 4:
                    raise ValueError("New username must be at least 4 characters")
                self.users[new_username] = self.users.pop(username)
                username = new_username
            
            if new_password:
                self.users[username]['password_hash'] = self._hash_password(new_password)
    
    def _transaction(self):
        """事务处理上下文管理器"""
        class Transaction:
            def __init__(self, manager):
                self.manager = manager
                self.backup = None
            
            def __enter__(self):
                self.backup = self.manager.users.copy()
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                if exc_type is not None:  # 发生异常时回滚
                    self.manager.users = self.backup
        
        return Transaction(self)
    
    def transfer_user_data(self, from_user, to_user):
        """安全转移用户数据，包含完整的事务处理"""
        if from_user == to_user:
            raise ValueError("Cannot transfer data to the same user")
        if from_user not in self.users or to_user not in self.users:
            raise ValueError("Both users must exist")
        
        with self._transaction():
            # 创建数据深拷贝避免引用问题
            data_copy = deepcopy(self.users[from_user]['data'])
            self.users[to_user]['data'].update(data_copy)
            self.users[from_user]['data'] = {}

class User:
    """用户数据封装类"""
    def __init__(self, username, data):
        self.username = username
        self.data = data
    
    def __repr__(self):
        return f"User(username='{self.username}', data={self.data})"

from copy import deepcopy
import unittest

class TestUserManager(unittest.TestCase):
    def setUp(self):
        self.manager = UserManager()
        self.manager.add_user("user1", "Passw0rd!")
        self.manager.add_user("user2", "Secure!123")
    
    def test_password_complexity(self):
        with self.assertRaises(ValueError):
            self.manager.add_user("test", "simple")
    
    def test_update_user(self):
        self.manager.update_user("user1", new_password="NewPass!123")
        with self.assertRaises(ValueError):
            self.manager.update_user("user1", new_password="weak")
    
    def test_transfer_data(self):
        self.manager.users["user1"]["data"] = {"key": "value"}
        self.manager.transfer_user_data("user1", "user2")
        self.assertEqual(self.manager.users["user2"]["data"], {"key": "value"})
        self.assertEqual(self.manager.users["user1"]["data"], {})
        
        with self.assertRaises(ValueError):
            self.manager.transfer_user_data("user1", "user1")

if __name__ == "__main__":
    unittest.main()